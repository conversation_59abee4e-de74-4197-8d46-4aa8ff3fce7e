from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
from typing import Optional, List
import traceback

from models import (
    GenerateImageRequest, UpscaleImageRequest, EditReferenceRequest, InpaintRequest,
    ApiResponse, GenerateImageResponse, StylesResponse, SessionResponse, ErrorResponse
)
from doubao_service import DoubaoService
from utils import logger, get_timestamp

# 创建FastAPI应用
app = FastAPI(
    title="豆包AI绘画API",
    description="基于豆包的AI绘画、图片编辑、抠图等功能的REST API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
try:
    doubao_service = DoubaoService()
    logger.info("[FastAPI] 豆包服务初始化成功")
except Exception as e:
    logger.error(f"[FastAPI] 豆包服务初始化失败: {e}")
    doubao_service = None

@app.get("/", response_model=ApiResponse)
async def root():
    """根路径 - API状态检查"""
    return ApiResponse(
        success=True,
        message="豆包AI绘画API服务正常运行",
        data={"version": "1.0.0", "status": "running"}
    )

@app.post("/api/generate", response_model=GenerateImageResponse)
async def generate_image(request: GenerateImageRequest):
    """AI绘画 - 生成图片"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")
        
        logger.info(f"[API] 收到生成图片请求: {request.prompt}")
        
        result = doubao_service.generate_image(
            prompt=request.prompt,
            style=request.style.value if request.style else None,
            ratio=request.ratio.value if request.ratio else None
        )
        
        if result["success"]:
            return GenerateImageResponse(
                success=True,
                message="图片生成成功",
                data={
                    "image_id": result["image_id"],
                    "urls": result["urls"],
                    "type": "generate",
                    "create_time": int(result["operation_params"]["conversation_id"]) if result["operation_params"]["conversation_id"] else 0,
                    "operation_params": result["operation_params"]
                }
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 生成图片错误: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upscale", response_model=ApiResponse)
async def upscale_image(request: UpscaleImageRequest):
    """图片放大 - 查看原图"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")
        
        logger.info(f"[API] 收到图片放大请求: {request.image_id}, 序号: {request.index}")
        
        result = doubao_service.upscale_image(request.image_id, request.index)
        
        if result["success"]:
            return ApiResponse(
                success=True,
                message="图片放大成功",
                data=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 图片放大错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/extract-subject", response_model=ApiResponse)
async def extract_subject(file: UploadFile = File(...)):
    """抠图 - 抠出图片主体"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")
        
        logger.info(f"[API] 收到抠图请求: {file.filename}")
        
        # 读取上传的图片
        image_data = await file.read()
        
        result = doubao_service.extract_subject(image_data)
        
        if result["success"]:
            return ApiResponse(
                success=True,
                message="抠图成功",
                data=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 抠图错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/edit-reference", response_model=ApiResponse)
async def edit_reference(
    prompt: str = Form(...),
    style: Optional[str] = Form(None),
    ratio: Optional[str] = Form(None),
    file: UploadFile = File(...)
):
    """参考图编辑 - 上传图片进行编辑"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")

        logger.info(f"[API] 收到参考图编辑请求: {prompt}, 文件: {file.filename}")

        # 读取上传的图片
        image_data = await file.read()

        # 调用参考图编辑功能
        result = doubao_service.edit_reference_image(
            prompt=prompt,
            image_data=image_data,
            style=style,
            ratio=ratio
        )

        if result["success"]:
            return ApiResponse(
                success=True,
                message="参考图编辑成功",
                data=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 参考图编辑错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/inpaint", response_model=ApiResponse)
async def inpaint_image(
    prompt: str = Form(...),
    mode: str = Form("circle"),
    is_invert: bool = Form(False),
    original_file: UploadFile = File(...),
    mask_file: UploadFile = File(...)
):
    """区域重绘 - 圈选或涂抹指定区域进行编辑"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")

        logger.info(f"[API] 收到区域重绘请求: {prompt}, 模式: {mode}")

        # 读取上传的图片
        original_data = await original_file.read()
        mask_data = await mask_file.read()

        # 调用区域重绘功能
        result = doubao_service.inpaint_image(
            prompt=prompt,
            original_data=original_data,
            mask_data=mask_data,
            mode=mode,
            is_invert=is_invert
        )

        if result["success"]:
            return ApiResponse(
                success=True,
                message="区域重绘成功",
                data=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 区域重绘错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/new-session", response_model=SessionResponse)
async def create_new_session():
    """强制新建会话"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")
        
        logger.info("[API] 收到新建会话请求")
        
        result = doubao_service.create_new_session()
        
        if result["success"]:
            return SessionResponse(
                success=True,
                message="新会话创建成功",
                data=result
            )
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "创建会话失败"))
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 创建新会话错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/styles", response_model=StylesResponse)
async def get_styles():
    """获取支持的风格列表"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")
        
        styles = doubao_service.get_supported_styles()
        
        return StylesResponse(
            success=True,
            message="获取风格列表成功",
            data=styles
        )
            
    except Exception as e:
        logger.error(f"[API] 获取风格列表错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/image/{image_id}")
async def get_image(image_id: str):
    """获取图片信息"""
    try:
        if not doubao_service:
            raise HTTPException(status_code=500, detail="服务未初始化")
        
        image_data = doubao_service.image_storage.get_image(image_id)
        if not image_data:
            raise HTTPException(status_code=404, detail="图片不存在")
        
        return ApiResponse(
            success=True,
            message="获取图片信息成功",
            data=image_data
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 获取图片信息错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    logger.info("[FastAPI] 启动豆包AI绘画API服务...")
    uvicorn.run(
        "fastapi_app:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
