import logging
import os
from datetime import datetime
from typing import Optional

# 配置日志系统
def setup_logger(name: str = "doubao", level: int = logging.INFO) -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        # 创建日志目录
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置日志级别
        logger.setLevel(level)
        
        # 创建文件处理器
        log_file = os.path.join(log_dir, f"{name}_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器到日志记录器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger

# 创建全局日志记录器
logger = setup_logger()

def save_image_to_local(image_data: bytes, filename: str, output_dir: str = "output") -> str:
    """保存图片到本地"""
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, filename)
    
    with open(filepath, 'wb') as f:
        f.write(image_data)
    
    logger.info(f"图片已保存到: {filepath}")
    return filepath

def save_text_to_local(text: str, filename: str, output_dir: str = "output") -> str:
    """保存文本到本地"""
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(text)
    
    logger.info(f"文本已保存到: {filepath}")
    return filepath

def get_timestamp() -> str:
    """获取时间戳字符串"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def ensure_directory(path: str) -> None:
    """确保目录存在"""
    os.makedirs(path, exist_ok=True)
